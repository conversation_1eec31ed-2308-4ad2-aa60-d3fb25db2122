-- 垂直入射法隔声量功能数据库表设计
-- 基于现有项目架构，新增隔声量表

USE nvh_data;

-- 隔声量表
CREATE TABLE sound_transmission_loss (
    id INT PRIMARY KEY AUTO_INCREMENT,
    part_id INT NOT NULL COMMENT '零件ID',
    material_id INT NOT NULL COMMENT '材料ID',
    manufacturer_id INT COMMENT '厂家ID',
    test_institution VARCHAR(100) COMMENT '测试机构',
    
    -- 测试值 (200-10000Hz中心频率)
    test_value_200 DECIMAL(5,2) COMMENT '200Hz测试值(dB)',
    test_value_250 DECIMAL(5,2) COMMENT '250Hz测试值(dB)',
    test_value_315 DECIMAL(5,2) COMMENT '315Hz测试值(dB)',
    test_value_400 DECIMAL(5,2) COMMENT '400Hz测试值(dB)',
    test_value_500 DECIMAL(5,2) COMMENT '500Hz测试值(dB)',
    test_value_630 DECIMAL(5,2) COMMENT '630Hz测试值(dB)',
    test_value_800 DECIMAL(5,2) COMMENT '800Hz测试值(dB)',
    test_value_1000 DECIMAL(5,2) COMMENT '1000Hz测试值(dB)',
    test_value_1250 DECIMAL(5,2) COMMENT '1250Hz测试值(dB)',
    test_value_1600 DECIMAL(5,2) COMMENT '1600Hz测试值(dB)',
    test_value_2000 DECIMAL(5,2) COMMENT '2000Hz测试值(dB)',
    test_value_2500 DECIMAL(5,2) COMMENT '2500Hz测试值(dB)',
    test_value_3150 DECIMAL(5,2) COMMENT '3150Hz测试值(dB)',
    test_value_4000 DECIMAL(5,2) COMMENT '4000Hz测试值(dB)',
    test_value_5000 DECIMAL(5,2) COMMENT '5000Hz测试值(dB)',
    test_value_6300 DECIMAL(5,2) COMMENT '6300Hz测试值(dB)',
    test_value_8000 DECIMAL(5,2) COMMENT '8000Hz测试值(dB)',
    test_value_10000 DECIMAL(5,2) COMMENT '10000Hz测试值(dB)',
    
    -- 目标值 (200-10000Hz中心频率)
    target_value_200 DECIMAL(5,2) COMMENT '200Hz目标值(dB)',
    target_value_250 DECIMAL(5,2) COMMENT '250Hz目标值(dB)',
    target_value_315 DECIMAL(5,2) COMMENT '315Hz目标值(dB)',
    target_value_400 DECIMAL(5,2) COMMENT '400Hz目标值(dB)',
    target_value_500 DECIMAL(5,2) COMMENT '500Hz目标值(dB)',
    target_value_630 DECIMAL(5,2) COMMENT '630Hz目标值(dB)',
    target_value_800 DECIMAL(5,2) COMMENT '800Hz目标值(dB)',
    target_value_1000 DECIMAL(5,2) COMMENT '1000Hz目标值(dB)',
    target_value_1250 DECIMAL(5,2) COMMENT '1250Hz目标值(dB)',
    target_value_1600 DECIMAL(5,2) COMMENT '1600Hz目标值(dB)',
    target_value_2000 DECIMAL(5,2) COMMENT '2000Hz目标值(dB)',
    target_value_2500 DECIMAL(5,2) COMMENT '2500Hz目标值(dB)',
    target_value_3150 DECIMAL(5,2) COMMENT '3150Hz目标值(dB)',
    target_value_4000 DECIMAL(5,2) COMMENT '4000Hz目标值(dB)',
    target_value_5000 DECIMAL(5,2) COMMENT '5000Hz目标值(dB)',
    target_value_6300 DECIMAL(5,2) COMMENT '6300Hz目标值(dB)',
    target_value_8000 DECIMAL(5,2) COMMENT '8000Hz目标值(dB)',
    target_value_10000 DECIMAL(5,2) COMMENT '10000Hz目标值(dB)',
    
    -- 测试信息
    test_date DATE COMMENT '测试日期',
    test_location VARCHAR(100) COMMENT '测试地点',
    test_engineer VARCHAR(50) COMMENT '测试工程师',
    test_image_path VARCHAR(500) COMMENT '测试图片路径',
    remarks TEXT COMMENT '备注',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 外键约束
    FOREIGN KEY (part_id) REFERENCES sound_insulation_parts(id) ON DELETE CASCADE,
    FOREIGN KEY (material_id) REFERENCES materials(id) ON DELETE CASCADE,
    FOREIGN KEY (manufacturer_id) REFERENCES material_manufacturers(id) ON DELETE SET NULL,
    
    -- 唯一约束：同一零件+材料组合只能有一条记录
    UNIQUE KEY uk_part_material (part_id, material_id)
) COMMENT '垂直入射法隔声量表';

-- 插入测试数据
-- 1. 前围隔音垫 + PET纤维毡 10mm 800g/m²
INSERT INTO sound_transmission_loss (
    part_id, material_id, manufacturer_id, test_institution,
    test_value_200, test_value_250, test_value_315, test_value_400, test_value_500,
    test_value_630, test_value_800, test_value_1000, test_value_1250, test_value_1600,
    test_value_2000, test_value_2500, test_value_3150, test_value_4000, test_value_5000,
    test_value_6300, test_value_8000, test_value_10000,
    target_value_200, target_value_250, target_value_315, target_value_400, target_value_500,
    target_value_630, target_value_800, target_value_1000, target_value_1250, target_value_1600,
    target_value_2000, target_value_2500, target_value_3150, target_value_4000, target_value_5000,
    target_value_6300, target_value_8000, target_value_10000,
    test_date, test_location, test_engineer, remarks
) VALUES (
    1, 1, 1, '中汽研声学实验室',
    15.2, 18.5, 22.1, 25.8, 28.9,
    31.2, 33.5, 35.8, 37.2, 38.9,
    40.1, 41.5, 42.8, 44.2, 45.6,
    46.8, 47.9, 48.5,
    15.0, 18.0, 22.0, 25.5, 28.5,
    31.0, 33.0, 35.5, 37.0, 38.5,
    40.0, 41.0, 42.5, 44.0, 45.0,
    46.5, 47.5, 48.0,
    '2024-01-15', '中汽研天津基地', '张工', '测试条件：温度23±2℃，湿度50±5%RH'
);

-- 2. 地毯 + PET纤维毡 15mm 1200g/m²
INSERT INTO sound_transmission_loss (
    part_id, material_id, manufacturer_id, test_institution,
    test_value_200, test_value_250, test_value_315, test_value_400, test_value_500,
    test_value_630, test_value_800, test_value_1000, test_value_1250, test_value_1600,
    test_value_2000, test_value_2500, test_value_3150, test_value_4000, test_value_5000,
    test_value_6300, test_value_8000, test_value_10000,
    target_value_200, target_value_250, target_value_315, target_value_400, target_value_500,
    target_value_630, target_value_800, target_value_1000, target_value_1250, target_value_1600,
    target_value_2000, target_value_2500, target_value_3150, target_value_4000, target_value_5000,
    target_value_6300, target_value_8000, target_value_10000,
    test_date, test_location, test_engineer, remarks
) VALUES (
    2, 2, 2, '同济大学声学实验室',
    18.5, 22.1, 26.8, 30.2, 33.5,
    36.8, 39.2, 41.5, 43.1, 44.8,
    46.2, 47.6, 48.9, 50.1, 51.3,
    52.4, 53.2, 53.8,
    18.0, 22.0, 26.5, 30.0, 33.0,
    36.5, 39.0, 41.0, 43.0, 44.5,
    46.0, 47.5, 48.5, 50.0, 51.0,
    52.0, 53.0, 53.5,
    '2024-01-20', '同济大学嘉定校区', '李工', '测试条件：温度23±2℃，湿度50±5%RH'
);

-- 3. 顶棚 + 玻璃纤维毡 12mm 1000g/m²
INSERT INTO sound_transmission_loss (
    part_id, material_id, manufacturer_id, test_institution,
    test_value_200, test_value_250, test_value_315, test_value_400, test_value_500,
    test_value_630, test_value_800, test_value_1000, test_value_1250, test_value_1600,
    test_value_2000, test_value_2500, test_value_3150, test_value_4000, test_value_5000,
    test_value_6300, test_value_8000, test_value_10000,
    target_value_200, target_value_250, target_value_315, target_value_400, target_value_500,
    target_value_630, target_value_800, target_value_1000, target_value_1250, target_value_1600,
    target_value_2000, target_value_2500, target_value_3150, target_value_4000, target_value_5000,
    target_value_6300, target_value_8000, target_value_10000,
    test_date, test_location, test_engineer, remarks
) VALUES (
    3, 4, 3, '清华大学声学实验室',
    16.8, 20.5, 24.2, 27.9, 31.1,
    34.3, 36.8, 39.2, 41.0, 42.6,
    44.1, 45.5, 46.8, 48.0, 49.2,
    50.3, 51.1, 51.8,
    16.5, 20.0, 24.0, 27.5, 31.0,
    34.0, 36.5, 39.0, 40.5, 42.5,
    44.0, 45.0, 46.5, 47.5, 49.0,
    50.0, 51.0, 51.5,
    '2024-01-25', '清华大学汽车工程系', '王工', '测试条件：温度23±2℃，湿度50±5%RH'
);
