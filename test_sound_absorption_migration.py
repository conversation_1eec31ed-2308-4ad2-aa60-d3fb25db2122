#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试吸音系数表结构优化后的功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from models.sound_absorption_models import (
    SoundInsulationPartModel, 
    MaterialModel, 
    MaterialManufacturerModel,
    SoundAbsorptionCoefficientModel
)
from services.sound_absorption_service import SoundAbsorptionService

def test_models():
    """测试模型功能"""
    print("=== 测试模型功能 ===")
    
    with app.app_context():
        try:
            # 测试零件模型
            parts = SoundInsulationPartModel.get_all_parts()
            print(f"✓ 零件模型测试成功，共 {len(parts)} 个零件")
            for part in parts:
                print(f"  - ID: {part.id}, 名称: {part.part_name}")
            
            # 测试材料模型
            materials = MaterialModel.get_all_materials()
            print(f"✓ 材料模型测试成功，共 {len(materials)} 个材料规格")
            for material in materials:
                print(f"  - ID: {material.id}, {material.display_name}")
            
            # 测试厂家模型
            manufacturers = MaterialManufacturerModel.get_all_manufacturers()
            print(f"✓ 厂家模型测试成功，共 {len(manufacturers)} 个厂家")
            for mf in manufacturers:
                print(f"  - ID: {mf.id}, 名称: {mf.manufacturer_name}")
            
            # 测试吸声系数模型
            coefficients = SoundAbsorptionCoefficientModel.query.all()
            print(f"✓ 吸声系数模型测试成功，共 {len(coefficients)} 条数据")
            for coeff in coefficients:
                print(f"  - ID: {coeff.id}, {coeff}")
            
            # 测试关联查询
            if coefficients:
                first_coeff = coefficients[0]
                print(f"\n测试关联数据访问:")
                print(f"  零件: {first_coeff.part_name}")
                print(f"  材料: {first_coeff.material_name}")
                print(f"  厚度: {first_coeff.thickness}mm")
                print(f"  克重: {first_coeff.weight}g/m²")
                print(f"  厂家: {first_coeff.manufacturer_name}")
            
            # 测试兼容性方法
            if parts and materials:
                part_name = parts[0].part_name
                material_name = materials[0].material_name
                
                weights = SoundAbsorptionCoefficientModel.get_weights_by_part_material(part_name, material_name)
                print(f"✓ 克重查询测试成功，{part_name}-{material_name} 有 {len(weights)} 个克重: {weights}")
                
                if weights:
                    data = SoundAbsorptionCoefficientModel.get_data_by_conditions(part_name, material_name, weights[0])
                    if data:
                        print("✓ 数据查询测试成功")
                        freq_data = data.get_test_frequency_data()
                        print(f"  - 125Hz测试值: {freq_data.get('125Hz')}")
                        print(f"  - 1000Hz测试值: {freq_data.get('1000Hz')}")
                        
                        # 测试新的字典转换方法
                        data_dict = data.to_dict_with_frequency_data()
                        print(f"  - 包含关联数据的字典转换成功，键数量: {len(data_dict)}")
                    else:
                        print("✗ 数据查询测试失败")
            
        except Exception as e:
            print(f"✗ 模型测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    return True

def test_services():
    """测试服务层"""
    print("\n=== 测试业务服务 ===")
    
    with app.app_context():
        try:
            service = SoundAbsorptionService()
            
            # 测试获取零件列表
            parts = service.get_part_list()
            print(f"✓ 获取零件列表成功，共 {len(parts)} 个零件")
            for part in parts:
                print(f"  - {part['name']}")
            
            # 测试获取材料列表
            materials = service.get_material_list()
            print(f"✓ 获取材料列表成功，共 {len(materials)} 个材料")
            for material in materials:
                print(f"  - {material['name']}")
            
            if parts and materials:
                part_name = parts[0]['name']
                material_name = materials[0]['name']
                
                # 测试根据零件获取材料
                part_materials = service.get_material_list(part_name)
                print(f"✓ 根据零件获取材料成功，{part_name} 有 {len(part_materials)} 个材料")
                
                # 测试获取克重列表
                weights = service.get_weight_list(part_name, material_name)
                print(f"✓ 获取克重列表成功，{part_name}-{material_name} 有 {len(weights)} 个克重")
                for weight in weights:
                    print(f"  - {weight['display']}")
                
                if weights:
                    weight_value = weights[0]['weight']
                    
                    # 测试获取吸音系数数据
                    data = service.get_absorption_data(part_name, material_name, weight_value)
                    if data:
                        print("✓ 获取吸音系数数据成功")
                        basic_info = data.get('basic_info', {})
                        print(f"  - 零件: {basic_info.get('part_name')}")
                        print(f"  - 材料: {basic_info.get('material_name')}")
                        print(f"  - 厚度: {basic_info.get('thickness')}mm")
                        print(f"  - 克重: {basic_info.get('weight')}g/m²")
                        print(f"  - 厂家: {basic_info.get('material_manufacturer')}")
                        print(f"  - 测试机构: {basic_info.get('test_institution')}")
                    else:
                        print("✗ 获取吸音系数数据失败")
                        return False
            
        except Exception as e:
            print(f"✗ 服务测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    return True

def test_api_compatibility():
    """测试API兼容性"""
    print("\n=== 测试API兼容性 ===")
    
    with app.test_client() as client:
        try:
            # 模拟登录（简化处理）
            with client.session_transaction() as sess:
                sess['user_id'] = 1
                sess['username'] = 'test_user'
            
            # 测试获取零件列表API
            response = client.get('/sound_absorption/api/parts')
            if response.status_code == 200:
                data = response.get_json()
                if data.get('code'):
                    print(f"✓ 零件列表API测试成功，返回 {len(data['data'])} 个零件")
                else:
                    print(f"✗ 零件列表API返回错误: {data.get('message')}")
                    return False
            else:
                print(f"✗ 零件列表API请求失败，状态码: {response.status_code}")
                return False
            
            # 测试获取材料列表API
            response = client.get('/sound_absorption/api/materials')
            if response.status_code == 200:
                data = response.get_json()
                if data.get('code'):
                    print(f"✓ 材料列表API测试成功，返回 {len(data['data'])} 个材料")
                else:
                    print(f"✗ 材料列表API返回错误: {data.get('message')}")
                    return False
            else:
                print(f"✗ 材料列表API请求失败，状态码: {response.status_code}")
                return False
            
            # 如果有数据，测试更多API
            parts_data = client.get('/sound_absorption/api/parts').get_json()['data']
            materials_data = client.get('/sound_absorption/api/materials').get_json()['data']
            
            if parts_data and materials_data:
                part_name = parts_data[0]['name']
                material_name = materials_data[0]['name']
                
                # 测试获取克重列表API
                response = client.get(f'/sound_absorption/api/weights?part_name={part_name}&material_name={material_name}')
                if response.status_code == 200:
                    data = response.get_json()
                    if data.get('code'):
                        weights = data['data']
                        print(f"✓ 克重列表API测试成功，返回 {len(weights)} 个克重")
                        
                        if weights:
                            weight = weights[0]['weight']
                            
                            # 测试获取吸音系数数据API
                            response = client.get(f'/sound_absorption/api/absorption_data?part_name={part_name}&material_name={material_name}&weight={weight}')
                            if response.status_code == 200:
                                data = response.get_json()
                                if data.get('code'):
                                    print("✓ 吸音系数数据API测试成功")
                                    absorption_data = data['data']
                                    basic_info = absorption_data.get('basic_info', {})
                                    print(f"  - 零件: {basic_info.get('part_name')}")
                                    print(f"  - 材料: {basic_info.get('material_name')}")
                                    print(f"  - 厚度: {basic_info.get('thickness')}mm")
                                    print(f"  - 克重: {basic_info.get('weight')}g/m²")
                                else:
                                    print(f"✗ 吸音系数数据API返回错误: {data.get('message')}")
                                    return False
                            else:
                                print(f"✗ 吸音系数数据API请求失败，状态码: {response.status_code}")
                                return False
                    else:
                        print(f"✗ 克重列表API返回错误: {data.get('message')}")
                        return False
                else:
                    print(f"✗ 克重列表API请求失败，状态码: {response.status_code}")
                    return False
            
        except Exception as e:
            print(f"✗ API兼容性测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    return True

def main():
    """主测试函数"""
    print("开始测试吸音系数表结构优化后的功能...\n")
    
    success = True
    
    # 测试模型
    if not test_models():
        success = False
    
    # 测试服务
    if not test_services():
        success = False
    
    # 测试API兼容性
    if not test_api_compatibility():
        success = False
    
    print(f"\n{'='*50}")
    if success:
        print("🎉 所有测试通过！表结构优化成功，功能正常运行。")
        print("\n建议:")
        print("1. 在生产环境中进行更全面的测试")
        print("2. 确认前端功能正常后，可以删除备份表")
        print("3. 更新相关文档")
    else:
        print("❌ 部分测试失败，请检查问题并修复。")
        print("\n如需回滚，请运行:")
        print("python execute_sound_absorption_migration.py rollback")
    
    return success

if __name__ == '__main__':
    main()
