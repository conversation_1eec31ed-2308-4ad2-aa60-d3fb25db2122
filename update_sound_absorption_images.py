#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新吸音系数测试数据的图片路径
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from models import db
from models.sound_absorption_models import SoundAbsorptionCoefficientModel

def update_test_image_paths():
    """更新测试图片路径"""
    print("🖼️ 更新吸音系数测试图片路径")
    print("=" * 50)
    
    with app.app_context():
        try:
            # 获取所有吸音系数数据
            all_data = SoundAbsorptionCoefficientModel.query.all()
            print(f"找到 {len(all_data)} 条吸音系数数据")
            
            # 为每条数据添加测试图片路径
            updated_count = 0
            for data in all_data:
                if not data.test_image_path:  # 只更新没有图片路径的数据
                    # 根据零件名称、材料名称和克重生成图片路径
                    part_safe = data.part_name.replace(' ', '_')
                    material_safe = data.material_name.replace(' ', '_')
                    weight_safe = str(int(data.weight))
                    
                    # 生成图片文件名
                    image_filename = f"sound_absorption_{part_safe}_{material_safe}_{weight_safe}g.jpg"
                    image_path = f"test_photos/sound_absorption/{image_filename}"
                    
                    # 更新数据库
                    data.test_image_path = image_path
                    updated_count += 1
                    
                    print(f"✓ 更新: {data.part_name} - {data.material_name} - {data.weight}g/m²")
                    print(f"  图片路径: {image_path}")
            
            # 提交更改
            if updated_count > 0:
                db.session.commit()
                print(f"\n🎉 成功更新 {updated_count} 条数据的图片路径")
            else:
                print("\n📝 所有数据都已有图片路径，无需更新")
                
        except Exception as e:
            print(f"✗ 更新失败: {e}")
            db.session.rollback()
            import traceback
            traceback.print_exc()
            return False
    
    return True

def create_sample_images():
    """创建示例图片目录结构"""
    print("\n📁 创建示例图片目录结构")
    
    # 创建目录
    upload_dir = "static/uploads/test_photos/sound_absorption"
    os.makedirs(upload_dir, exist_ok=True)
    print(f"✓ 创建目录: {upload_dir}")
    
    # 创建一个示例图片文件（空文件，用于测试）
    sample_files = [
        "sound_absorption_前围隔音垫_PET纤维毡_800g.jpg",
        "sound_absorption_前围隔音垫_PET纤维毡_1200g.jpg", 
        "sound_absorption_前围隔音垫_PET纤维毡_1600g.jpg"
    ]
    
    for filename in sample_files:
        filepath = os.path.join(upload_dir, filename)
        if not os.path.exists(filepath):
            # 创建一个空的图片文件占位符
            with open(filepath, 'w') as f:
                f.write("# 这是一个测试图片占位符文件\n")
                f.write(f"# 文件名: {filename}\n")
                f.write(f"# 创建时间: {datetime.now()}\n")
            print(f"✓ 创建示例文件: {filename}")
        else:
            print(f"📄 文件已存在: {filename}")

if __name__ == '__main__':
    print("🚀 开始更新吸音系数测试图片路径...")
    
    # 更新数据库中的图片路径
    success = update_test_image_paths()
    
    if success:
        # 创建示例图片目录和文件
        create_sample_images()
        print("\n✅ 所有操作完成！")
        print("\n📋 接下来的步骤:")
        print("1. 将实际的测试图片文件放入 static/uploads/test_photos/sound_absorption/ 目录")
        print("2. 确保图片文件名与数据库中的路径匹配")
        print("3. 测试'查看测试附图'功能")
    else:
        print("\n❌ 更新失败！")
        sys.exit(1)
