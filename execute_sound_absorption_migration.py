#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
执行吸音系数表结构优化迁移
"""

import pymysql
from config import Config
import sys

def execute_migration():
    """执行数据库迁移"""
    try:
        # 连接数据库
        connection = pymysql.connect(
            host=Config.MYSQL_HOST,
            port=Config.MYSQL_PORT,
            user=Config.MYSQL_USER,
            password=Config.MYSQL_PASSWORD,
            database=Config.MYSQL_DATABASE,
            charset='utf8mb4'
        )
        
        print(f"✓ 成功连接到数据库 {Config.MYSQL_DATABASE}")
        
        with connection.cursor() as cursor:
            # 读取迁移SQL文件内容
            with open('migrate_sound_absorption_tables.sql', 'r', encoding='utf-8') as f:
                sql_content = f.read()
            
            # 分割SQL语句（以分号分割）
            sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
            
            print(f"准备执行 {len(sql_statements)} 个SQL语句...")
            
            # 执行每个SQL语句
            for i, statement in enumerate(sql_statements):
                if statement.upper().startswith('USE'):
                    continue  # 跳过USE语句，因为已经连接到指定数据库
                
                try:
                    cursor.execute(statement)
                    print(f"✓ 执行SQL语句 {i+1}/{len(sql_statements)}")
                    
                    # 如果是查询语句，显示结果
                    if statement.strip().upper().startswith('SELECT'):
                        results = cursor.fetchall()
                        if results:
                            print("查询结果:")
                            for row in results:
                                print(f"  {row}")
                        else:
                            print("  无结果")
                            
                except Exception as e:
                    if "already exists" in str(e).lower() or "duplicate" in str(e).lower():
                        print(f"⚠ 跳过已存在的对象: {e}")
                    else:
                        print(f"✗ 执行SQL语句失败: {e}")
                        print(f"语句内容: {statement[:200]}...")
                        # 对于关键错误，询问是否继续
                        if "foreign key" in str(e).lower() or "constraint" in str(e).lower():
                            response = input("遇到约束错误，是否继续？(y/n): ")
                            if response.lower() != 'y':
                                raise e
            
            # 提交事务
            connection.commit()
            print("✓ 所有SQL语句执行完成，事务已提交")
            
            # 验证迁移结果
            print("\n=== 验证迁移结果 ===")
            
            # 检查新表结构
            cursor.execute("DESCRIBE sound_absorption_coefficients")
            columns = cursor.fetchall()
            print("新表结构:")
            for col in columns:
                print(f"  {col[0]} - {col[1]} - {col[2]}")
            
            # 检查数据完整性
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_records,
                    COUNT(DISTINCT part_id) as unique_parts,
                    COUNT(DISTINCT material_id) as unique_materials,
                    COUNT(DISTINCT manufacturer_id) as unique_manufacturers
                FROM sound_absorption_coefficients
            """)
            stats = cursor.fetchone()
            print(f"\n数据统计:")
            print(f"  总记录数: {stats[0]}")
            print(f"  唯一零件数: {stats[1]}")
            print(f"  唯一材料数: {stats[2]}")
            print(f"  唯一厂家数: {stats[3]}")
            
            # 检查关联数据
            cursor.execute("""
                SELECT 
                    p.part_name,
                    m.material_name,
                    m.thickness,
                    m.weight,
                    mf.manufacturer_name,
                    COUNT(*) as record_count
                FROM sound_absorption_coefficients sac
                LEFT JOIN sound_insulation_parts p ON p.id = sac.part_id
                LEFT JOIN materials m ON m.id = sac.material_id
                LEFT JOIN material_manufacturers mf ON mf.id = sac.manufacturer_id
                GROUP BY p.part_name, m.material_name, m.thickness, m.weight, mf.manufacturer_name
                ORDER BY p.part_name, m.material_name, m.weight
            """)
            
            data_summary = cursor.fetchall()
            print(f"\n数据关联验证:")
            for row in data_summary:
                print(f"  {row[0]} - {row[1]} ({row[2]}mm, {row[3]}g/m²) - {row[4]} - {row[5]}条记录")
        
        connection.close()
        print("\n🎉 数据库迁移完成！")
        print("\n下一步:")
        print("1. 重启应用程序")
        print("2. 运行测试脚本验证功能")
        print("3. 如果一切正常，可以删除备份表")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据库迁移失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def rollback_migration():
    """回滚迁移（恢复备份）"""
    try:
        connection = pymysql.connect(
            host=Config.MYSQL_HOST,
            port=Config.MYSQL_PORT,
            user=Config.MYSQL_USER,
            password=Config.MYSQL_PASSWORD,
            database=Config.MYSQL_DATABASE,
            charset='utf8mb4'
        )
        
        print(f"✓ 连接数据库进行回滚操作")
        
        with connection.cursor() as cursor:
            # 检查备份表是否存在
            cursor.execute("SHOW TABLES LIKE 'sound_absorption_coefficients_backup'")
            if not cursor.fetchone():
                print("✗ 未找到备份表，无法回滚")
                return False
            
            # 删除当前表
            cursor.execute("DROP TABLE IF EXISTS sound_absorption_coefficients")
            
            # 恢复备份表
            cursor.execute("RENAME TABLE sound_absorption_coefficients_backup TO sound_absorption_coefficients")
            
            # 恢复materials表
            cursor.execute("DROP TABLE IF EXISTS materials")
            cursor.execute("RENAME TABLE materials_backup TO materials")
            
            connection.commit()
            print("✓ 回滚完成")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"✗ 回滚失败: {e}")
        return False

if __name__ == '__main__':
    if len(sys.argv) > 1 and sys.argv[1] == 'rollback':
        print("执行回滚操作...")
        rollback_migration()
    else:
        print("执行数据库迁移...")
        success = execute_migration()
        if not success:
            print("\n如需回滚，请运行: python execute_sound_absorption_migration.py rollback")
