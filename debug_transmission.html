<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>隔声量功能调试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
</head>
<body>
    <div class="container mt-4">
        <h1>隔声量功能调试</h1>
        
        <div class="row mb-3">
            <div class="col-md-3">
                <button class="btn btn-primary" onclick="testAPI()">测试API</button>
            </div>
            <div class="col-md-3">
                <button class="btn btn-success" onclick="testDisplay()">测试显示</button>
            </div>
        </div>
        
        <div id="test-results" class="mb-4">
            <h3>测试结果：</h3>
            <pre id="api-result"></pre>
        </div>
        
        <!-- 数据展示区域 -->
        <div id="data-display" style="display: none;">
            <div class="card mb-4">
                <div class="card-header">
                    <h6>基础信息</h6>
                </div>
                <div class="card-body">
                    <div id="basic-info"></div>
                </div>
            </div>
            
            <div class="table-responsive mb-4">
                <table class="table table-striped" id="data-table">
                    <thead class="table-dark">
                        <tr>
                            <th>数据类型</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
            
            <div class="mb-4">
                <h6>隔声量曲线图</h6>
                <div id="chart-container" style="width: 100%; height: 400px;"></div>
            </div>
        </div>
    </div>

    <script>
        let testData = null;
        let chart = null;

        async function testAPI() {
            try {
                console.log('开始测试API...');
                const response = await fetch('/sound_transmission/api/transmission_data?part_name=前围隔音垫&material_name=PET纤维毡&weight=800');
                const result = await response.json();
                
                console.log('API响应:', result);
                document.getElementById('api-result').textContent = JSON.stringify(result, null, 2);
                
                if (result.code === 200) {
                    testData = result.data;
                    console.log('API测试成功，数据已保存');
                } else {
                    console.error('API返回错误:', result.message);
                }
            } catch (error) {
                console.error('API测试失败:', error);
                document.getElementById('api-result').textContent = 'API测试失败: ' + error.message;
            }
        }

        function testDisplay() {
            if (!testData) {
                alert('请先测试API获取数据');
                return;
            }
            
            console.log('开始测试显示功能...');
            displayBasicInfo(testData);
            displayDataTable(testData);
            displayChart(testData);
            $('#data-display').show();
            console.log('显示测试完成');
        }

        function displayBasicInfo(data) {
            console.log('显示基础信息...');
            const basicInfo = $('#basic-info');
            basicInfo.empty();
            
            const infoItems = [
                { label: '零件名称', value: data.part_name },
                { label: '材料名称', value: data.material_name },
                { label: '厚度', value: data.thickness ? `${data.thickness}mm` : '-' },
                { label: '克重', value: data.weight ? `${data.weight}g/m²` : '-' },
                { label: '测试机构', value: data.test_institution || '-' }
            ];
            
            infoItems.forEach(item => {
                basicInfo.append(`
                    <div class="col-md-4 mb-2">
                        <strong>${item.label}:</strong> ${item.value}
                    </div>
                `);
            });
        }

        function displayDataTable(data) {
            console.log('显示数据表格...');
            const table = $('#data-table');
            const thead = table.find('thead tr');
            const tbody = table.find('tbody');
            
            // 清空表格
            thead.find('th:not(:first)').remove();
            tbody.empty();
            
            // 频率标签
            const frequencies = ['200Hz', '250Hz', '315Hz', '400Hz', '500Hz', '630Hz', '800Hz', '1000Hz', 
                               '1250Hz', '1600Hz', '2000Hz', '2500Hz', '3150Hz', '4000Hz', '5000Hz', 
                               '6300Hz', '8000Hz', '10000Hz'];
            
            // 添加频率列标题
            frequencies.forEach(freq => {
                thead.append(`<th class="text-center">${freq}</th>`);
            });
            
            // 添加测试值行
            const testRow = $('<tr><td class="fw-bold">测试值(dB)</td></tr>');
            frequencies.forEach(freq => {
                const value = data.test_frequency_data[freq];
                const displayValue = value !== null && value !== undefined ? value.toFixed(2) : '-';
                testRow.append(`<td class="text-center">${displayValue}</td>`);
            });
            tbody.append(testRow);
            
            // 添加目标值行
            const targetRow = $('<tr><td class="fw-bold">目标值(dB)</td></tr>');
            frequencies.forEach(freq => {
                const value = data.target_frequency_data[freq];
                const displayValue = value !== null && value !== undefined ? value.toFixed(2) : '-';
                targetRow.append(`<td class="text-center">${displayValue}</td>`);
            });
            tbody.append(targetRow);
            
            console.log('表格显示完成');
        }

        function displayChart(data) {
            console.log('显示图表...');
            const chartContainer = document.getElementById('chart-container');
            
            if (chart) {
                chart.dispose();
            }
            
            if (typeof echarts === 'undefined') {
                console.error('ECharts未加载');
                return;
            }
            
            chart = echarts.init(chartContainer);
            
            // 准备数据
            const frequencies = ['200Hz', '250Hz', '315Hz', '400Hz', '500Hz', '630Hz', '800Hz', '1000Hz', 
                               '1250Hz', '1600Hz', '2000Hz', '2500Hz', '3150Hz', '4000Hz', '5000Hz', 
                               '6300Hz', '8000Hz', '10000Hz'];
            
            const testValues = frequencies.map(freq => {
                const value = data.test_frequency_data[freq];
                return value !== null && value !== undefined ? value : null;
            });
            
            const targetValues = frequencies.map(freq => {
                const value = data.target_frequency_data[freq];
                return value !== null && value !== undefined ? value : null;
            });
            
            const option = {
                title: {
                    text: '隔声量对比曲线',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data: ['测试值', '目标值'],
                    top: 30
                },
                xAxis: {
                    type: 'category',
                    data: frequencies,
                    name: '频率'
                },
                yAxis: {
                    type: 'value',
                    name: '隔声量(dB)'
                },
                series: [
                    {
                        name: '测试值',
                        type: 'line',
                        data: testValues,
                        lineStyle: { color: '#1890ff' }
                    },
                    {
                        name: '目标值',
                        type: 'line',
                        data: targetValues,
                        lineStyle: { color: '#52c41a', type: 'dashed' }
                    }
                ]
            };
            
            chart.setOption(option);
            console.log('图表显示完成');
        }
    </script>
</body>
</html>
