-- 垂直入射法吸音系数功能数据库表设计
-- 基于现有项目架构，新增4个表

USE nvh_data;

-- 1. 吸隔声零件表
CREATE TABLE sound_insulation_parts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    part_name VARCHAR(100) NOT NULL COMMENT '零件名称',
    description TEXT COMMENT '零件描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_part_name (part_name)
) COMMENT '吸隔声零件表';

-- 2. 材料规格表
CREATE TABLE materials (
    id INT PRIMARY KEY AUTO_INCREMENT,
    material_name VARCHAR(100) NOT NULL COMMENT '材料名称',
    thickness DECIMAL(6,2) NOT NULL COMMENT '厚度(mm)',
    weight DECIMAL(8,2) NOT NULL COMMENT '克重(g/m²)',
    description TEXT COMMENT '材料描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_material_spec (material_name, thickness, weight)
) COMMENT '材料规格表';

-- 3. 材料厂家表
CREATE TABLE material_manufacturers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    manufacturer_name VARCHAR(100) NOT NULL COMMENT '厂家名称',
    description TEXT COMMENT '厂家描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_manufacturer_name (manufacturer_name)
) COMMENT '材料厂家表';

-- 4. 吸声系数表（规范化）
CREATE TABLE sound_absorption_coefficients (
    id INT PRIMARY KEY AUTO_INCREMENT,
    part_id INT NOT NULL COMMENT '零件ID',
    material_id INT NOT NULL COMMENT '材料ID',
    manufacturer_id INT COMMENT '厂家ID',
    test_institution VARCHAR(100) COMMENT '测试机构',
    
    -- 测试值 (19个频率点: 125Hz-10000Hz)
    test_value_125 DECIMAL(4,3) COMMENT '125Hz测试值',
    test_value_160 DECIMAL(4,3) COMMENT '160Hz测试值',
    test_value_200 DECIMAL(4,3) COMMENT '200Hz测试值',
    test_value_250 DECIMAL(4,3) COMMENT '250Hz测试值',
    test_value_315 DECIMAL(4,3) COMMENT '315Hz测试值',
    test_value_400 DECIMAL(4,3) COMMENT '400Hz测试值',
    test_value_500 DECIMAL(4,3) COMMENT '500Hz测试值',
    test_value_630 DECIMAL(4,3) COMMENT '630Hz测试值',
    test_value_800 DECIMAL(4,3) COMMENT '800Hz测试值',
    test_value_1000 DECIMAL(4,3) COMMENT '1000Hz测试值',
    test_value_1250 DECIMAL(4,3) COMMENT '1250Hz测试值',
    test_value_1600 DECIMAL(4,3) COMMENT '1600Hz测试值',
    test_value_2000 DECIMAL(4,3) COMMENT '2000Hz测试值',
    test_value_2500 DECIMAL(4,3) COMMENT '2500Hz测试值',
    test_value_3150 DECIMAL(4,3) COMMENT '3150Hz测试值',
    test_value_4000 DECIMAL(4,3) COMMENT '4000Hz测试值',
    test_value_5000 DECIMAL(4,3) COMMENT '5000Hz测试值',
    test_value_6300 DECIMAL(4,3) COMMENT '6300Hz测试值',
    test_value_8000 DECIMAL(4,3) COMMENT '8000Hz测试值',
    test_value_10000 DECIMAL(4,3) COMMENT '10000Hz测试值',
    
    -- 目标值 (19个频率点: 125Hz-10000Hz)
    target_value_125 DECIMAL(4,3) COMMENT '125Hz目标值',
    target_value_160 DECIMAL(4,3) COMMENT '160Hz目标值',
    target_value_200 DECIMAL(4,3) COMMENT '200Hz目标值',
    target_value_250 DECIMAL(4,3) COMMENT '250Hz目标值',
    target_value_315 DECIMAL(4,3) COMMENT '315Hz目标值',
    target_value_400 DECIMAL(4,3) COMMENT '400Hz目标值',
    target_value_500 DECIMAL(4,3) COMMENT '500Hz目标值',
    target_value_630 DECIMAL(4,3) COMMENT '630Hz目标值',
    target_value_800 DECIMAL(4,3) COMMENT '800Hz目标值',
    target_value_1000 DECIMAL(4,3) COMMENT '1000Hz目标值',
    target_value_1250 DECIMAL(4,3) COMMENT '1250Hz目标值',
    target_value_1600 DECIMAL(4,3) COMMENT '1600Hz目标值',
    target_value_2000 DECIMAL(4,3) COMMENT '2000Hz目标值',
    target_value_2500 DECIMAL(4,3) COMMENT '2500Hz目标值',
    target_value_3150 DECIMAL(4,3) COMMENT '3150Hz目标值',
    target_value_4000 DECIMAL(4,3) COMMENT '4000Hz目标值',
    target_value_5000 DECIMAL(4,3) COMMENT '5000Hz目标值',
    target_value_6300 DECIMAL(4,3) COMMENT '6300Hz目标值',
    target_value_8000 DECIMAL(4,3) COMMENT '8000Hz目标值',
    target_value_10000 DECIMAL(4,3) COMMENT '10000Hz目标值',
    
    -- 测试信息
    test_date DATE COMMENT '测试日期',
    test_location VARCHAR(100) COMMENT '测试地点',
    test_engineer VARCHAR(50) COMMENT '测试工程师',
    test_image_path VARCHAR(500) COMMENT '测试图片路径',
    remarks TEXT COMMENT '备注',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- 外键约束
    FOREIGN KEY (part_id) REFERENCES sound_insulation_parts(id),
    FOREIGN KEY (material_id) REFERENCES materials(id),
    FOREIGN KEY (manufacturer_id) REFERENCES material_manufacturers(id),

    -- 索引
    INDEX idx_part_material (part_id, material_id),
    INDEX idx_test_date (test_date),
    UNIQUE KEY uk_part_material_test (part_id, material_id, test_date)
) COMMENT '吸声系数表（规范化）';

-- 插入测试数据
-- 1. 零件数据
INSERT INTO sound_insulation_parts (part_name, description) VALUES
('前围隔音垫', '发动机舱与驾驶室之间的隔音材料'),
('地毯', '车内地板隔音吸音材料'),
('顶棚', '车顶内饰隔音吸音材料'),
('门板内饰', '车门内侧隔音吸音材料'),
('后围隔音垫', '后备箱与驾驶室之间的隔音材料');

-- 2. 材料数据
INSERT INTO materials (material_name, thickness, weight, description) VALUES
('PET纤维毡', 10.0, 800, '聚酯纤维毡材料'),
('PET纤维毡', 15.0, 1200, '聚酯纤维毡材料'),
('PET纤维毡', 20.0, 1600, '聚酯纤维毡材料'),
('玻璃纤维毡', 12.0, 1000, '玻璃纤维毡材料'),
('玻璃纤维毡', 18.0, 1500, '玻璃纤维毡材料'),
('聚氨酯泡沫', 25.0, 600, '聚氨酯发泡材料'),
('聚氨酯泡沫', 30.0, 800, '聚氨酯发泡材料');

-- 3. 厂家数据
INSERT INTO material_manufacturers (manufacturer_name, description) VALUES
('奥托立夫', '汽车安全系统供应商'),
('佛吉亚', '汽车零部件供应商'),
('延锋', '汽车内饰供应商'),
('李尔', '汽车座椅和内饰供应商'),
('麦格纳', '汽车零部件供应商');

-- 4. 吸声系数测试数据（示例）
INSERT INTO sound_absorption_coefficients (
    part_id, material_id, manufacturer_id, test_institution,
    test_value_125, test_value_160, test_value_200, test_value_250, test_value_315, test_value_400,
    test_value_500, test_value_630, test_value_800, test_value_1000, test_value_1250, test_value_1600,
    test_value_2000, test_value_2500, test_value_3150, test_value_4000, test_value_5000, test_value_6300,
    test_value_8000, test_value_10000,
    target_value_125, target_value_160, target_value_200, target_value_250, target_value_315, target_value_400,
    target_value_500, target_value_630, target_value_800, target_value_1000, target_value_1250, target_value_1600,
    target_value_2000, target_value_2500, target_value_3150, target_value_4000, target_value_5000, target_value_6300,
    target_value_8000, target_value_10000,
    test_date, test_location, test_engineer, remarks
) VALUES
-- 前围隔音垫 - PET纤维毡 - 不同克重 (使用ID引用)
(1, 1, 1, '上汽通用五菱声学实验室',
 0.08, 0.12, 0.15, 0.18, 0.22, 0.26, 0.31, 0.35, 0.40, 0.45, 0.50, 0.55, 0.60, 0.65, 0.70, 0.75, 0.78, 0.80, 0.82, 0.85,
 0.10, 0.15, 0.18, 0.20, 0.25, 0.30, 0.35, 0.40, 0.45, 0.50, 0.55, 0.60, 0.65, 0.70, 0.75, 0.80, 0.82, 0.85, 0.87, 0.90,
 '2024-01-15', '柳州', '张工', '800g/m²克重测试'),

(1, 2, 1, '上汽通用五菱声学实验室',
 0.12, 0.16, 0.20, 0.24, 0.28, 0.32, 0.38, 0.42, 0.47, 0.52, 0.57, 0.62, 0.67, 0.72, 0.77, 0.82, 0.85, 0.87, 0.89, 0.92,
 0.10, 0.15, 0.18, 0.20, 0.25, 0.30, 0.35, 0.40, 0.45, 0.50, 0.55, 0.60, 0.65, 0.70, 0.75, 0.80, 0.82, 0.85, 0.87, 0.90,
 '2024-01-16', '柳州', '张工', '1200g/m²克重测试'),

(1, 3, 1, '上汽通用五菱声学实验室',
 0.15, 0.20, 0.25, 0.30, 0.35, 0.40, 0.45, 0.50, 0.55, 0.60, 0.65, 0.70, 0.75, 0.80, 0.85, 0.88, 0.90, 0.92, 0.94, 0.96,
 0.10, 0.15, 0.18, 0.20, 0.25, 0.30, 0.35, 0.40, 0.45, 0.50, 0.55, 0.60, 0.65, 0.70, 0.75, 0.80, 0.82, 0.85, 0.87, 0.90,
 '2024-01-17', '柳州', '张工', '1600g/m²克重测试');
