#!/usr/bin/env python3
"""
上墙法隔声量数据库初始化脚本
"""

import os
import sys
from datetime import datetime
from flask import Flask
from config import Config
from models import db
from models.sound_absorption_models import (
    SoundInsulationPartModel,
    MaterialModel,
    MaterialManufacturerModel,
    WallMountedTransmissionLossModel
)

def create_app():
    """创建Flask应用"""
    app = Flask(__name__)
    app.config.from_object(Config)
    db.init_app(app)
    return app

def init_database():
    """初始化数据库表"""
    print("🔧 创建数据库表...")
    
    try:
        # 创建所有表
        db.create_all()
        print("✅ 数据库表创建成功")
        return True
    except Exception as e:
        print(f"❌ 数据库表创建失败: {str(e)}")
        return False

def init_sample_data():
    """初始化示例数据"""
    print("📊 初始化示例数据...")
    
    try:
        # 检查是否已有数据
        if WallMountedTransmissionLossModel.query.first():
            print("⚠️  数据库中已存在上墙法隔声量数据，跳过初始化")
            return True
        
        # 确保基础数据存在
        ensure_basic_data()
        
        # 示例数据
        sample_data = [
            {
                'part_name': '前围隔音垫',
                'material_name': 'PET纤维毡',
                'thickness': 10.0,
                'weight': 800.0,
                'manufacturer_name': '奥托立夫',
                'test_institution': '中汽研',
                'test_date': '2024-01-15',
                'test_location': '天津实验室',
                'test_engineer': '张工',
                'remarks': '上墙法隔声量测试',
                'test_values': [15.2, 18.5, 22.1, 25.8, 28.9, 31.2, 33.5, 35.8, 37.2, 38.9, 
                               40.1, 41.5, 42.8, 43.9, 44.8, 45.5, 46.2, 46.8, 47.3, 47.8],
                'target_values': [12.0, 15.0, 18.0, 22.0, 25.0, 28.0, 30.0, 32.0, 34.0, 36.0,
                                 38.0, 39.0, 40.0, 41.0, 42.0, 43.0, 44.0, 45.0, 46.0, 47.0]
            },
            {
                'part_name': '前围隔音垫',
                'material_name': 'PET纤维毡',
                'thickness': 15.0,
                'weight': 1200.0,
                'manufacturer_name': '奥托立夫',
                'test_institution': '中汽研',
                'test_date': '2024-01-16',
                'test_location': '天津实验室',
                'test_engineer': '李工',
                'remarks': '上墙法隔声量测试',
                'test_values': [18.5, 22.1, 26.8, 30.2, 33.5, 36.1, 38.8, 41.2, 43.5, 45.1,
                               46.8, 48.2, 49.5, 50.8, 51.9, 52.8, 53.5, 54.1, 54.6, 55.0],
                'target_values': [15.0, 18.0, 22.0, 26.0, 29.0, 32.0, 35.0, 37.0, 39.0, 41.0,
                                 43.0, 44.0, 45.0, 46.0, 47.0, 48.0, 49.0, 50.0, 51.0, 52.0]
            },
            {
                'part_name': '地毯',
                'material_name': '玻璃纤维毡',
                'thickness': 12.0,
                'weight': 1000.0,
                'manufacturer_name': '佛吉亚',
                'test_institution': '中汽研',
                'test_date': '2024-01-20',
                'test_location': '天津实验室',
                'test_engineer': '王工',
                'remarks': '上墙法隔声量测试',
                'test_values': [16.8, 20.2, 24.5, 28.1, 31.8, 34.5, 37.2, 39.8, 42.1, 44.2,
                               46.1, 47.8, 49.2, 50.5, 51.6, 52.5, 53.2, 53.8, 54.3, 54.7],
                'target_values': [14.0, 17.0, 21.0, 25.0, 28.0, 31.0, 34.0, 36.0, 38.0, 40.0,
                                 42.0, 43.0, 44.0, 45.0, 46.0, 47.0, 48.0, 49.0, 50.0, 51.0]
            }
        ]
        
        # 获取频率列名
        freq_columns = WallMountedTransmissionLossModel.get_frequency_columns()
        target_freq_columns = WallMountedTransmissionLossModel.get_target_frequency_columns()
        
        for data in sample_data:
            # 获取或创建零件
            part = SoundInsulationPartModel.query.filter_by(part_name=data['part_name']).first()
            if not part:
                part = SoundInsulationPartModel(
                    part_name=data['part_name'],
                    description=f"{data['part_name']}零件"
                )
                db.session.add(part)
                db.session.flush()
            
            # 获取或创建材料
            material = MaterialModel.query.filter_by(
                material_name=data['material_name'],
                thickness=data['thickness'],
                weight=data['weight']
            ).first()
            if not material:
                material = MaterialModel(
                    material_name=data['material_name'],
                    thickness=data['thickness'],
                    weight=data['weight'],
                    description=f"{data['material_name']} {data['thickness']}mm {data['weight']}g/m²"
                )
                db.session.add(material)
                db.session.flush()
            
            # 获取或创建厂家
            manufacturer = MaterialManufacturerModel.query.filter_by(
                manufacturer_name=data['manufacturer_name']
            ).first()
            if not manufacturer:
                manufacturer = MaterialManufacturerModel(
                    manufacturer_name=data['manufacturer_name'],
                    description=f"{data['manufacturer_name']}厂家"
                )
                db.session.add(manufacturer)
                db.session.flush()
            
            # 创建上墙法隔声量记录
            transmission_loss = WallMountedTransmissionLossModel(
                part_id=part.id,
                material_id=material.id,
                manufacturer_id=manufacturer.id,
                test_institution=data['test_institution'],
                test_date=datetime.strptime(data['test_date'], '%Y-%m-%d').date(),
                test_location=data['test_location'],
                test_engineer=data['test_engineer'],
                remarks=data['remarks']
            )
            
            # 设置测试值
            for i, value in enumerate(data['test_values']):
                if i < len(freq_columns):
                    setattr(transmission_loss, freq_columns[i], value)
            
            # 设置目标值
            for i, value in enumerate(data['target_values']):
                if i < len(target_freq_columns):
                    setattr(transmission_loss, target_freq_columns[i], value)
            
            db.session.add(transmission_loss)
            print(f"✓ 添加数据：{data['part_name']} - {data['material_name']} - {data['weight']}g/m²")
        
        db.session.commit()
        print("✅ 示例数据初始化成功")
        return True
        
    except Exception as e:
        db.session.rollback()
        print(f"❌ 示例数据初始化失败: {str(e)}")
        return False

def ensure_basic_data():
    """确保基础数据存在"""
    # 基础零件数据
    basic_parts = [
        {'name': '前围隔音垫', 'desc': '前围隔音垫零件'},
        {'name': '地毯', 'desc': '地毯零件'},
        {'name': '顶棚', 'desc': '顶棚零件'},
        {'name': '门板内饰', 'desc': '门板内饰零件'},
        {'name': '后围隔音垫', 'desc': '后围隔音垫零件'}
    ]
    
    for part_data in basic_parts:
        if not SoundInsulationPartModel.query.filter_by(part_name=part_data['name']).first():
            part = SoundInsulationPartModel(
                part_name=part_data['name'],
                description=part_data['desc']
            )
            db.session.add(part)
    
    # 基础厂家数据
    basic_manufacturers = [
        {'name': '奥托立夫', 'desc': '奥托立夫汽车安全系统'},
        {'name': '佛吉亚', 'desc': '佛吉亚汽车零部件'},
        {'name': '延锋', 'desc': '延锋汽车内饰'},
        {'name': '李尔', 'desc': '李尔汽车座椅'},
        {'name': '麦格纳', 'desc': '麦格纳汽车零部件'}
    ]
    
    for mfr_data in basic_manufacturers:
        if not MaterialManufacturerModel.query.filter_by(manufacturer_name=mfr_data['name']).first():
            manufacturer = MaterialManufacturerModel(
                manufacturer_name=mfr_data['name'],
                description=mfr_data['desc']
            )
            db.session.add(manufacturer)
    
    db.session.commit()

def main():
    """主函数"""
    print("🚀 开始初始化上墙法隔声量数据库...")
    
    app = create_app()
    
    with app.app_context():
        # 初始化数据库表
        if not init_database():
            sys.exit(1)
        
        # 初始化示例数据
        if not init_sample_data():
            sys.exit(1)
    
    print("🎉 上墙法隔声量数据库初始化完成！")
    print("\n📋 初始化内容:")
    print("   - 创建了 wall_mounted_transmission_loss 表")
    print("   - 添加了 3 条示例数据")
    print("   - 包含 20 个频率点 (125Hz-10000Hz)")
    print("\n🌐 访问地址:")
    print("   http://localhost:5000/wall_mounted_transmission/transmission_loss_query")

if __name__ == '__main__':
    main()
