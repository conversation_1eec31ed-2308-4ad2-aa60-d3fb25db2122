#!/usr/bin/env python3
"""
上墙法隔声量功能测试脚本
"""

import requests
import json
from flask import Flask
from config import Config
from models import db
from models.sound_absorption_models import WallMountedTransmissionLossModel

def create_app():
    """创建Flask应用"""
    app = Flask(__name__)
    app.config.from_object(Config)
    db.init_app(app)
    return app

def test_database_connection():
    """测试数据库连接"""
    print("🔍 测试数据库连接...")
    
    app = create_app()
    
    with app.app_context():
        try:
            # 测试查询总数
            total_count = WallMountedTransmissionLossModel.query.count()
            print(f"  上墙法隔声量记录总数: {total_count}")
            
            # 测试查询具体数据
            sample_data = WallMountedTransmissionLossModel.query.first()
            if sample_data:
                print(f"  ✓ 样本数据: {sample_data}")
                print(f"  零件: {sample_data.part_name}")
                print(f"  材料: {sample_data.material_name}")
                print(f"  克重: {sample_data.weight}g/m²")
            else:
                print("  ❌ 未找到上墙法隔声量数据")
                return False
            
            # 测试频率数据方法
            test_freq_data = sample_data.get_test_frequency_data()
            target_freq_data = sample_data.get_target_frequency_data()
            print(f"  ✓ 测试值数据点数: {len([v for v in test_freq_data.values() if v is not None])}")
            print(f"  ✓ 目标值数据点数: {len([v for v in target_freq_data.values() if v is not None])}")
            
            return True
            
        except Exception as e:
            print(f"  ❌ 数据库连接失败: {str(e)}")
            return False

def test_api_endpoints():
    """测试API接口"""
    print("🌐 测试API接口...")
    
    base_url = "http://localhost:5000"
    
    # 测试接口列表
    test_cases = [
        {
            'name': '获取零件列表',
            'url': f'{base_url}/wall_mounted_transmission/api/parts',
            'method': 'GET'
        },
        {
            'name': '获取材料列表',
            'url': f'{base_url}/wall_mounted_transmission/api/materials?part_name=前围隔音垫',
            'method': 'GET'
        },
        {
            'name': '获取克重列表',
            'url': f'{base_url}/wall_mounted_transmission/api/weights?part_name=前围隔音垫&material_name=PET纤维毡',
            'method': 'GET'
        },
        {
            'name': '获取隔声量数据',
            'url': f'{base_url}/wall_mounted_transmission/api/transmission_data?part_name=前围隔音垫&material_name=PET纤维毡&weight=800',
            'method': 'GET'
        },
        {
            'name': '获取测试图片信息',
            'url': f'{base_url}/wall_mounted_transmission/api/test_image?part_name=前围隔音垫&material_name=PET纤维毡&weight=800',
            'method': 'GET'
        },
        {
            'name': '获取厂家列表',
            'url': f'{base_url}/wall_mounted_transmission/api/manufacturers',
            'method': 'GET'
        },
        {
            'name': '搜索数据',
            'url': f'{base_url}/wall_mounted_transmission/api/search?part_name=前围',
            'method': 'GET'
        },
        {
            'name': '获取统计信息',
            'url': f'{base_url}/wall_mounted_transmission/api/statistics',
            'method': 'GET'
        }
    ]
    
    success_count = 0
    
    for test_case in test_cases:
        try:
            print(f"  测试: {test_case['name']}")
            
            if test_case['method'] == 'GET':
                response = requests.get(test_case['url'], timeout=10)
            else:
                response = requests.post(test_case['url'], timeout=10)
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if data.get('code') == 200:
                        print(f"    ✓ 成功 - 返回数据: {len(data.get('data', []))} 条")
                        success_count += 1
                    else:
                        print(f"    ❌ 业务错误 - {data.get('message', '未知错误')}")
                except json.JSONDecodeError:
                    print(f"    ❌ JSON解析失败")
            else:
                print(f"    ❌ HTTP错误 - 状态码: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"    ❌ 请求失败 - {str(e)}")
    
    print(f"  API测试完成: {success_count}/{len(test_cases)} 个接口正常")
    return success_count == len(test_cases)

def test_page_access():
    """测试页面访问"""
    print("📄 测试页面访问...")
    
    base_url = "http://localhost:5000"
    
    try:
        # 测试主页面
        response = requests.get(f'{base_url}/wall_mounted_transmission/transmission_loss_query', timeout=10)
        
        if response.status_code == 200:
            print("  ✓ 上墙法隔声量查询页面访问正常")
            
            # 检查页面内容
            content = response.text
            if '上墙法隔声量查询' in content:
                print("  ✓ 页面标题正确")
            if 'wall_mounted_transmission.js' in content:
                print("  ✓ JavaScript文件引用正确")
            if 'echarts' in content:
                print("  ✓ ECharts库引用正确")
            
            return True
        else:
            print(f"  ❌ 页面访问失败 - 状态码: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"  ❌ 页面访问失败 - {str(e)}")
        return False

def test_data_integrity():
    """测试数据完整性"""
    print("🔍 测试数据完整性...")
    
    app = create_app()
    
    with app.app_context():
        try:
            # 获取所有数据
            all_data = WallMountedTransmissionLossModel.query.all()
            
            if not all_data:
                print("  ❌ 没有找到任何数据")
                return False
            
            print(f"  ✓ 找到 {len(all_data)} 条数据记录")
            
            # 检查每条数据的完整性
            for i, data in enumerate(all_data):
                print(f"  检查记录 {i+1}: {data.part_name} - {data.material_name}")
                
                # 检查基本信息
                if not data.part_name:
                    print(f"    ❌ 缺少零件名称")
                    continue
                
                if not data.material_name:
                    print(f"    ❌ 缺少材料名称")
                    continue
                
                # 检查频率数据
                test_data = data.get_test_frequency_data()
                target_data = data.get_target_frequency_data()
                
                test_count = len([v for v in test_data.values() if v is not None])
                target_count = len([v for v in target_data.values() if v is not None])
                
                print(f"    ✓ 测试值: {test_count}/20 个频率点")
                print(f"    ✓ 目标值: {target_count}/20 个频率点")
                
                # 检查数据转换
                dict_data = data.to_dict_with_frequency_data()
                if 'test_frequency_data' in dict_data and 'target_frequency_data' in dict_data:
                    print(f"    ✓ 数据转换正常")
                else:
                    print(f"    ❌ 数据转换失败")
            
            return True
            
        except Exception as e:
            print(f"  ❌ 数据完整性检查失败: {str(e)}")
            return False

def main():
    """主函数"""
    print("🧪 开始测试上墙法隔声量功能...")
    print("=" * 50)
    
    # 测试数据库连接
    db_test = test_database_connection()
    print()
    
    # 测试数据完整性
    data_test = test_data_integrity()
    print()
    
    # 测试API接口
    api_test = test_api_endpoints()
    print()
    
    # 测试页面访问
    page_test = test_page_access()
    print()
    
    # 总结测试结果
    print("=" * 50)
    print("📊 测试结果总结:")
    print(f"  数据库连接: {'✅ 通过' if db_test else '❌ 失败'}")
    print(f"  数据完整性: {'✅ 通过' if data_test else '❌ 失败'}")
    print(f"  API接口: {'✅ 通过' if api_test else '❌ 失败'}")
    print(f"  页面访问: {'✅ 通过' if page_test else '❌ 失败'}")
    
    if all([db_test, data_test, api_test, page_test]):
        print("\n🎉 所有测试通过！上墙法隔声量功能运行正常。")
        print("\n🌐 访问地址:")
        print("   http://localhost:5000/wall_mounted_transmission/transmission_loss_query")
    else:
        print("\n⚠️  部分测试失败，请检查相关配置。")

if __name__ == '__main__':
    main()
