#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
垂直入射法隔声量功能测试脚本
"""

import os
import sys
import requests
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from models import db
from models.sound_absorption_models import SoundTransmissionLossModel

# 测试配置
BASE_URL = 'http://localhost:5000'
TEST_SESSION = requests.Session()

def test_api_endpoint(endpoint, method='GET', params=None, data=None):
    """测试API端点"""
    url = f"{BASE_URL}{endpoint}"
    
    try:
        if method == 'GET':
            response = TEST_SESSION.get(url, params=params)
        elif method == 'POST':
            response = TEST_SESSION.post(url, json=data)
        else:
            raise ValueError(f"不支持的HTTP方法: {method}")
        
        print(f"  {method} {endpoint}")
        print(f"  状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                if result.get('success'):
                    print(f"  ✓ 成功: {result.get('message', '')}")
                    return result.get('data')
                else:
                    print(f"  ❌ 失败: {result.get('message', '')}")
                    return None
            except json.JSONDecodeError:
                print(f"  ✓ 响应成功（非JSON格式）")
                return True
        else:
            print(f"  ❌ HTTP错误: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"  ❌ 请求异常: {str(e)}")
        return None

def test_database_operations():
    """测试数据库操作"""
    print("\n1. 测试数据库操作")
    print("-" * 30)
    
    with app.app_context():
        try:
            # 测试查询总数
            total_count = SoundTransmissionLossModel.query.count()
            print(f"  隔声量记录总数: {total_count}")
            
            # 测试查询具体数据
            sample_data = SoundTransmissionLossModel.query.first()
            if sample_data:
                print(f"  ✓ 样本数据: {sample_data}")
                print(f"  零件: {sample_data.part_name}")
                print(f"  材料: {sample_data.material_name}")
                print(f"  克重: {sample_data.weight}g/m²")
            else:
                print("  ❌ 未找到隔声量数据")
                return False
            
            # 测试频率数据方法
            test_freq_data = sample_data.get_test_frequency_data()
            target_freq_data = sample_data.get_target_frequency_data()
            print(f"  ✓ 测试值数据点数: {len([v for v in test_freq_data.values() if v is not None])}")
            print(f"  ✓ 目标值数据点数: {len([v for v in target_freq_data.values() if v is not None])}")
            
            return True
            
        except Exception as e:
            print(f"  ❌ 数据库操作失败: {str(e)}")
            return False

def test_api_endpoints():
    """测试API端点"""
    print("\n2. 测试API端点")
    print("-" * 30)
    
    # 测试获取零件列表
    print("\n2.1 获取零件列表")
    parts = test_api_endpoint('/sound_transmission/api/parts')
    if not parts:
        return False
    print(f"  零件数量: {len(parts)}")
    
    # 选择第一个零件进行后续测试
    if not parts:
        print("  ❌ 没有可用的零件数据")
        return False
    
    test_part = parts[0]['name']
    print(f"  测试零件: {test_part}")
    
    # 测试获取材料列表
    print("\n2.2 获取材料列表")
    materials = test_api_endpoint('/sound_transmission/api/materials', params={'part_name': test_part})
    if not materials:
        return False
    print(f"  材料数量: {len(materials)}")
    
    if not materials:
        print("  ❌ 没有可用的材料数据")
        return False
    
    test_material = materials[0]['name']
    print(f"  测试材料: {test_material}")
    
    # 测试获取克重列表
    print("\n2.3 获取克重列表")
    weights = test_api_endpoint('/sound_transmission/api/weights', params={
        'part_name': test_part,
        'material_name': test_material
    })
    if not weights:
        return False
    print(f"  克重数量: {len(weights)}")
    
    if not weights:
        print("  ❌ 没有可用的克重数据")
        return False
    
    test_weight = weights[0]['weight']
    print(f"  测试克重: {test_weight}g/m²")
    
    # 测试获取隔声量数据
    print("\n2.4 获取隔声量数据")
    transmission_data = test_api_endpoint('/sound_transmission/api/transmission_data', params={
        'part_name': test_part,
        'material_name': test_material,
        'weight': test_weight
    })
    if not transmission_data:
        return False
    
    print(f"  ✓ 获取到隔声量数据")
    print(f"  测试值数据点: {len([v for v in transmission_data['test_frequency_data'].values() if v is not None])}")
    print(f"  目标值数据点: {len([v for v in transmission_data['target_frequency_data'].values() if v is not None])}")
    
    # 测试统计信息
    print("\n2.5 获取统计信息")
    stats = test_api_endpoint('/sound_transmission/api/statistics')
    if stats:
        print(f"  总记录数: {stats['total_records']}")
        print(f"  零件数: {stats['total_parts']}")
        print(f"  材料数: {stats['total_materials']}")
        print(f"  厂家数: {stats['total_manufacturers']}")
    
    return True

def test_page_access():
    """测试页面访问"""
    print("\n3. 测试页面访问")
    print("-" * 30)
    
    # 测试主查询页面
    print("\n3.1 隔声量查询页面")
    result = test_api_endpoint('/sound_transmission/transmission_loss_query')
    if result:
        print("  ✓ 页面访问成功")
        return True
    else:
        print("  ❌ 页面访问失败")
        return False

def test_service_methods():
    """测试服务方法"""
    print("\n4. 测试服务方法")
    print("-" * 30)

    with app.app_context():
        try:
            from services.sound_transmission_service import SoundTransmissionService
            service = SoundTransmissionService()

            # 测试获取零件列表
            parts = service.get_part_list()
            print(f"  ✓ 零件列表: {len(parts)} 个")

            if parts:
                # 测试获取材料列表
                materials = service.get_material_list(parts[0]['name'])
                print(f"  ✓ 材料列表: {len(materials)} 个")

                if materials:
                    # 测试获取克重列表
                    weights = service.get_weight_list(parts[0]['name'], materials[0]['name'])
                    print(f"  ✓ 克重列表: {len(weights)} 个")

                    if weights:
                        # 测试获取隔声量数据
                        data = service.get_transmission_data(
                            parts[0]['name'],
                            materials[0]['name'],
                            weights[0]['weight']
                        )
                        if data:
                            print(f"  ✓ 隔声量数据获取成功")
                        else:
                            print(f"  ❌ 隔声量数据获取失败")
                            return False

            # 测试统计信息
            stats = service.get_statistics()
            print(f"  ✓ 统计信息: {stats}")

            return True

        except Exception as e:
            print(f"  ❌ 服务方法测试失败: {str(e)}")
            return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("垂直入射法隔声量功能测试")
    print("=" * 50)
    
    # 测试结果
    test_results = []
    
    # 1. 测试数据库操作
    test_results.append(("数据库操作", test_database_operations()))
    
    # 2. 测试服务方法
    test_results.append(("服务方法", test_service_methods()))
    
    # 3. 测试API端点（需要启动服务器）
    print("\n⚠ 注意：API和页面测试需要先启动Flask服务器")
    print("请在另一个终端运行: python app.py")
    
    user_input = input("\n是否继续API测试？(y/n): ").lower().strip()
    if user_input == 'y':
        test_results.append(("API端点", test_api_endpoints()))
        test_results.append(("页面访问", test_page_access()))
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    
    all_passed = True
    for test_name, result in test_results:
        status = "✓ 通过" if result else "❌ 失败"
        print(f"{test_name:20} {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有测试通过！隔声量功能部署成功！")
    else:
        print("⚠ 部分测试失败，请检查相关配置")
    print("=" * 50)

if __name__ == '__main__':
    main()
