<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>垂直入射法吸声系数表格优化演示</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* 表格优化样式 */
        .optimized-table {
            table-layout: fixed;
            width: 100%;
        }

        .optimized-table th:first-child,
        .optimized-table td:first-child {
            width: 60px;
            min-width: 60px;
            text-align: center;
            font-weight: bold;
        }

        .optimized-table th:not(:first-child),
        .optimized-table td:not(:first-child) {
            width: 80px;
            min-width: 80px;
            text-align: center;
        }

        .comparison-table {
            table-layout: fixed;
            width: 100%;
        }

        .comparison-table .sticky-column {
            width: 100px;
            min-width: 100px;
            text-align: center;
            font-weight: bold;
            background-color: #f8f9fa;
        }

        .comparison-table th:not(.sticky-column),
        .comparison-table td:not(.sticky-column) {
            width: 80px;
            min-width: 80px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="mb-4">垂直入射法吸声系数表格优化演示</h1>
        
        <div class="row">
            <div class="col-md-6">
                <h4 class="text-danger">修改前（垂直显示）</h4>
                <div class="table-responsive mb-4">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>频率</th>
                                <th>测试值</th>
                                <th>目标值</th>
                                <th>达标状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr><td><strong>125</strong></td><td>0.123</td><td>0.120</td><td class="text-success"><i class="fas fa-check me-1"></i>达标</td></tr>
                            <tr><td><strong>250</strong></td><td>0.234</td><td>0.230</td><td class="text-success"><i class="fas fa-check me-1"></i>达标</td></tr>
                            <tr><td><strong>500</strong></td><td>0.345</td><td>0.350</td><td class="text-danger"><i class="fas fa-times me-1"></i>不达标</td></tr>
                            <tr><td><strong>1000</strong></td><td>0.456</td><td>0.450</td><td class="text-success"><i class="fas fa-check me-1"></i>达标</td></tr>
                            <tr><td><strong>2000</strong></td><td>0.567</td><td>0.560</td><td class="text-success"><i class="fas fa-check me-1"></i>达标</td></tr>
                            <tr><td><strong>4000</strong></td><td>0.678</td><td>0.670</td><td class="text-success"><i class="fas fa-check me-1"></i>达标</td></tr>
                        </tbody>
                    </table>
                </div>
                <div class="alert alert-warning">
                    <strong>问题：</strong>
                    <ul class="mb-0">
                        <li>表格过高，占用垂直空间</li>
                        <li>达标状态列冗余</li>
                        <li>不便于横向对比频率数据</li>
                    </ul>
                </div>
            </div>
            
            <div class="col-md-6">
                <h4 class="text-success">修改后（横向显示）</h4>
                <div class="table-responsive mb-4">
                    <table class="table table-striped table-hover optimized-table">
                        <thead class="table-dark">
                            <tr>
                                <th>频率</th>
                                <th>125</th>
                                <th>250</th>
                                <th>500</th>
                                <th>1000</th>
                                <th>2000</th>
                                <th>4000</th>
                                <th>10000</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>测试</strong></td>
                                <td>0.123</td>
                                <td>0.234</td>
                                <td>0.345</td>
                                <td>0.456</td>
                                <td>0.567</td>
                                <td>0.678</td>
                                <td>0.789</td>
                            </tr>
                            <tr>
                                <td><strong>目标</strong></td>
                                <td>0.120</td>
                                <td>0.230</td>
                                <td>0.350</td>
                                <td>0.450</td>
                                <td>0.560</td>
                                <td>0.670</td>
                                <td>0.780</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="alert alert-success">
                    <strong>优势：</strong>
                    <ul class="mb-0">
                        <li>表格更紧凑，节省空间</li>
                        <li>删除冗余的达标状态列</li>
                        <li>便于横向对比各频率数据</li>
                        <li>第一列标签简洁（频率、测试、目标）</li>
                        <li>固定列宽，避免表格过宽</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <hr class="my-5">
        
        <h4 class="text-primary mb-3">多克重对比表格（横向显示）</h4>
        <div class="table-responsive mb-4">
            <table class="table table-striped table-hover table-sm comparison-table">
                <thead class="table-dark">
                    <tr>
                        <th class="sticky-column">频率</th>
                        <th>125</th>
                        <th>250</th>
                        <th>500</th>
                        <th>1000</th>
                        <th>2000</th>
                        <th>4000</th>
                        <th>8000</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="sticky-column"><strong>800测试</strong></td>
                        <td>0.123</td>
                        <td>0.234</td>
                        <td>0.345</td>
                        <td>0.456</td>
                        <td>0.567</td>
                        <td>0.678</td>
                        <td>0.789</td>
                    </tr>
                    <tr>
                        <td class="sticky-column"><strong>800目标</strong></td>
                        <td>0.120</td>
                        <td>0.230</td>
                        <td>0.350</td>
                        <td>0.450</td>
                        <td>0.560</td>
                        <td>0.670</td>
                        <td>0.780</td>
                    </tr>
                    <tr>
                        <td class="sticky-column"><strong>1000测试</strong></td>
                        <td>0.145</td>
                        <td>0.256</td>
                        <td>0.367</td>
                        <td>0.478</td>
                        <td>0.589</td>
                        <td>0.690</td>
                        <td>0.801</td>
                    </tr>
                    <tr>
                        <td class="sticky-column"><strong>1000目标</strong></td>
                        <td>0.140</td>
                        <td>0.250</td>
                        <td>0.370</td>
                        <td>0.470</td>
                        <td>0.580</td>
                        <td>0.680</td>
                        <td>0.790</td>
                    </tr>
                    <tr>
                        <td class="sticky-column"><strong>1200测试</strong></td>
                        <td>0.167</td>
                        <td>0.278</td>
                        <td>0.389</td>
                        <td>0.490</td>
                        <td>0.601</td>
                        <td>0.712</td>
                        <td>0.823</td>
                    </tr>
                    <tr>
                        <td class="sticky-column"><strong>1200目标</strong></td>
                        <td>0.160</td>
                        <td>0.270</td>
                        <td>0.390</td>
                        <td>0.490</td>
                        <td>0.600</td>
                        <td>0.710</td>
                        <td>0.820</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="alert alert-info">
            <h5><i class="fas fa-info-circle me-2"></i>修改总结</h5>
            <div class="row">
                <div class="col-md-6">
                    <h6>主要改动：</h6>
                    <ul>
                        <li>✅ 表格横向显示，频率作为列标题</li>
                        <li>✅ 第一列简化为"频率"、"测试"、"目标"</li>
                        <li>✅ 删除"达标状态"列</li>
                        <li>✅ 固定列宽：第一列60px，频率列80px</li>
                        <li>✅ 多克重对比使用"800测试"、"800目标"等简洁标签</li>
                        <li>✅ 去掉"Hz"单位，避免"10000Hz"换行显示</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>技术实现：</h6>
                    <ul>
                        <li>修改了 <code>displayDataTable()</code> 函数</li>
                        <li>修改了 <code>displayComparisonTable()</code> 函数</li>
                        <li>添加了CSS样式控制列宽</li>
                        <li>使用 <code>table-layout: fixed</code> 固定布局</li>
                        <li>第一列使用 <code>sticky-column</code> 样式</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
