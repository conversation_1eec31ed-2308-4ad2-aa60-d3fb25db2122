#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试关系映射
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from models.sound_absorption_models import (
    SoundInsulationPartModel, 
    MaterialModel, 
    MaterialManufacturerModel,
    SoundAbsorptionCoefficientModel
)

def test_relationships():
    """测试关系映射"""
    print("=== 测试关系映射 ===")
    
    with app.app_context():
        try:
            # 获取第一条吸声系数数据
            coeff = SoundAbsorptionCoefficientModel.query.first()
            if coeff:
                print(f"吸声系数记录ID: {coeff.id}")
                print(f"part_id: {coeff.part_id}")
                print(f"material_id: {coeff.material_id}")
                print(f"manufacturer_id: {coeff.manufacturer_id}")
                
                # 测试关系访问
                print(f"关联的零件: {coeff.part}")
                if coeff.part:
                    print(f"零件名称: {coeff.part.part_name}")
                
                print(f"关联的材料: {coeff.material}")
                if coeff.material:
                    print(f"材料名称: {coeff.material.material_name}")
                    print(f"厚度: {coeff.material.thickness}")
                    print(f"克重: {coeff.material.weight}")
                
                print(f"关联的厂家: {coeff.manufacturer}")
                if coeff.manufacturer:
                    print(f"厂家名称: {coeff.manufacturer.manufacturer_name}")
                
                # 测试属性访问
                print(f"\n通过属性访问:")
                print(f"part_name: {coeff.part_name}")
                print(f"material_name: {coeff.material_name}")
                print(f"thickness: {coeff.thickness}")
                print(f"weight: {coeff.weight}")
                print(f"manufacturer_name: {coeff.manufacturer_name}")
                
            else:
                print("没有找到吸声系数数据")
                
        except Exception as e:
            print(f"测试失败: {e}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    test_relationships()
