<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格显示测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container mt-4">
        <h2>垂直入射法吸声系数表格显示测试</h2>
        
        <h4 class="mt-4">修改前的表格结构（垂直显示）：</h4>
        <div class="table-responsive mb-4">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>频率</th>
                        <th>测试值</th>
                        <th>目标值</th>
                        <th>达标状态</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>125</strong></td>
                        <td>0.123</td>
                        <td>0.120</td>
                        <td class="text-success"><i class="fas fa-check me-1"></i>达标</td>
                    </tr>
                    <tr>
                        <td><strong>250</strong></td>
                        <td>0.234</td>
                        <td>0.230</td>
                        <td class="text-success"><i class="fas fa-check me-1"></i>达标</td>
                    </tr>
                    <tr>
                        <td><strong>500</strong></td>
                        <td>0.345</td>
                        <td>0.350</td>
                        <td class="text-danger"><i class="fas fa-times me-1"></i>不达标</td>
                    </tr>
                    <tr>
                        <td><strong>1000</strong></td>
                        <td>0.456</td>
                        <td>0.450</td>
                        <td class="text-success"><i class="fas fa-check me-1"></i>达标</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <h4 class="mt-4">修改后的表格结构（横向显示，优化列宽）：</h4>
        <div class="table-responsive mb-4">
            <table class="table table-striped table-hover" id="new-table" style="table-layout: fixed;">
                <thead class="table-dark">
                    <tr>
                        <th style="width: 60px; min-width: 60px; text-align: center;">频率</th>
                        <th class="text-center" style="width: 80px; min-width: 80px;">125</th>
                        <th class="text-center" style="width: 80px; min-width: 80px;">250</th>
                        <th class="text-center" style="width: 80px; min-width: 80px;">500</th>
                        <th class="text-center" style="width: 80px; min-width: 80px;">1000</th>
                        <th class="text-center" style="width: 80px; min-width: 80px;">2000</th>
                        <th class="text-center" style="width: 80px; min-width: 80px;">4000</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="text-align: center;"><strong>测试</strong></td>
                        <td class="text-center">0.123</td>
                        <td class="text-center">0.234</td>
                        <td class="text-center">0.345</td>
                        <td class="text-center">0.456</td>
                        <td class="text-center">0.567</td>
                        <td class="text-center">0.678</td>
                    </tr>
                    <tr>
                        <td style="text-align: center;"><strong>目标</strong></td>
                        <td class="text-center">0.120</td>
                        <td class="text-center">0.230</td>
                        <td class="text-center">0.350</td>
                        <td class="text-center">0.450</td>
                        <td class="text-center">0.560</td>
                        <td class="text-center">0.670</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <h4 class="mt-4">多克重对比表格（横向显示，优化列宽）：</h4>
        <div class="table-responsive mb-4">
            <table class="table table-striped table-hover table-sm" style="table-layout: fixed;">
                <thead class="table-dark">
                    <tr>
                        <th style="width: 100px; min-width: 100px; text-align: center; background-color: #495057;">频率</th>
                        <th class="text-center" style="width: 80px; min-width: 80px;">125</th>
                        <th class="text-center" style="width: 80px; min-width: 80px;">250</th>
                        <th class="text-center" style="width: 80px; min-width: 80px;">500</th>
                        <th class="text-center" style="width: 80px; min-width: 80px;">1000</th>
                        <th class="text-center" style="width: 80px; min-width: 80px;">2000</th>
                        <th class="text-center" style="width: 80px; min-width: 80px;">4000</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="width: 100px; text-align: center; background-color: #f8f9fa;"><strong>800测试</strong></td>
                        <td class="text-center">0.123</td>
                        <td class="text-center">0.234</td>
                        <td class="text-center">0.345</td>
                        <td class="text-center">0.456</td>
                        <td class="text-center">0.567</td>
                        <td class="text-center">0.678</td>
                    </tr>
                    <tr>
                        <td style="width: 100px; text-align: center; background-color: #f8f9fa;"><strong>800目标</strong></td>
                        <td class="text-center">0.120</td>
                        <td class="text-center">0.230</td>
                        <td class="text-center">0.350</td>
                        <td class="text-center">0.450</td>
                        <td class="text-center">0.560</td>
                        <td class="text-center">0.670</td>
                    </tr>
                    <tr>
                        <td style="width: 100px; text-align: center; background-color: #f8f9fa;"><strong>1000测试</strong></td>
                        <td class="text-center">0.145</td>
                        <td class="text-center">0.256</td>
                        <td class="text-center">0.367</td>
                        <td class="text-center">0.478</td>
                        <td class="text-center">0.589</td>
                        <td class="text-center">0.690</td>
                    </tr>
                    <tr>
                        <td style="width: 100px; text-align: center; background-color: #f8f9fa;"><strong>1000目标</strong></td>
                        <td class="text-center">0.140</td>
                        <td class="text-center">0.250</td>
                        <td class="text-center">0.370</td>
                        <td class="text-center">0.470</td>
                        <td class="text-center">0.580</td>
                        <td class="text-center">0.680</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="alert alert-info">
            <h5>修改说明：</h5>
            <ul>
                <li>✅ 第一行现在显示125-10000的中心频率作为列标题</li>
                <li>✅ 第一列现在显示简洁的"测试"和"目标"作为行标题</li>
                <li>✅ 删除了"达标状态"列</li>
                <li>✅ 表格从垂直显示改为横向显示，更加紧凑</li>
                <li>✅ 多克重对比表格也采用相同的横向显示方式</li>
                <li>✅ 优化了列宽：第一列60px，频率列80px，避免表格过宽</li>
                <li>✅ 多克重对比中使用"800测试"、"800目标"等简洁标签</li>
            </ul>
        </div>
    </div>
</body>
</html>
