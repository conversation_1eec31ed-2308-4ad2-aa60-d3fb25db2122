#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
垂直入射法隔声量功能数据库初始化脚本
"""

import os
import sys
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from models import db
from models.sound_absorption_models import (
    SoundInsulationPartModel,
    MaterialModel,
    MaterialManufacturerModel,
    SoundTransmissionLossModel
)

def init_database():
    """初始化数据库"""
    with app.app_context():
        try:
            # 创建表
            print("正在创建隔声量数据表...")
            db.create_all()
            print("✓ 数据表创建成功")
            
            # 检查是否已有隔声量数据
            existing_data = SoundTransmissionLossModel.query.first()
            if existing_data:
                print("⚠ 数据库中已存在隔声量数据，跳过初始化")
                return
            
            # 获取现有的零件、材料、厂家数据
            parts = SoundInsulationPartModel.query.all()
            materials = MaterialModel.query.all()
            manufacturers = MaterialManufacturerModel.query.all()
            
            if not parts or not materials or not manufacturers:
                print("⚠ 缺少基础数据（零件、材料、厂家），请先初始化吸音系数数据")
                return
            
            print("正在插入隔声量测试数据...")
            
            # 插入测试数据
            test_data = [
                {
                    'part_name': '前围隔音垫',
                    'material_name': 'PET纤维毡',
                    'weight': 800,
                    'manufacturer_name': '奥托立夫',
                    'test_institution': '中汽研声学实验室',
                    'test_values': [15.2, 18.5, 22.1, 25.8, 28.9, 31.2, 33.5, 35.8, 37.2, 38.9, 40.1, 41.5, 42.8, 44.2, 45.6, 46.8, 47.9, 48.5],
                    'target_values': [15.0, 18.0, 22.0, 25.5, 28.5, 31.0, 33.0, 35.5, 37.0, 38.5, 40.0, 41.0, 42.5, 44.0, 45.0, 46.5, 47.5, 48.0],
                    'test_date': '2024-01-15',
                    'test_location': '中汽研天津基地',
                    'test_engineer': '张工',
                    'remarks': '测试条件：温度23±2℃，湿度50±5%RH'
                },
                {
                    'part_name': '地毯',
                    'material_name': 'PET纤维毡',
                    'weight': 1200,
                    'manufacturer_name': '佛吉亚',
                    'test_institution': '同济大学声学实验室',
                    'test_values': [18.5, 22.1, 26.8, 30.2, 33.5, 36.8, 39.2, 41.5, 43.1, 44.8, 46.2, 47.6, 48.9, 50.1, 51.3, 52.4, 53.2, 53.8],
                    'target_values': [18.0, 22.0, 26.5, 30.0, 33.0, 36.5, 39.0, 41.0, 43.0, 44.5, 46.0, 47.5, 48.5, 50.0, 51.0, 52.0, 53.0, 53.5],
                    'test_date': '2024-01-20',
                    'test_location': '同济大学嘉定校区',
                    'test_engineer': '李工',
                    'remarks': '测试条件：温度23±2℃，湿度50±5%RH'
                },
                {
                    'part_name': '顶棚',
                    'material_name': '玻璃纤维毡',
                    'weight': 1000,
                    'manufacturer_name': '延锋',
                    'test_institution': '清华大学声学实验室',
                    'test_values': [16.8, 20.5, 24.2, 27.9, 31.1, 34.3, 36.8, 39.2, 41.0, 42.6, 44.1, 45.5, 46.8, 48.0, 49.2, 50.3, 51.1, 51.8],
                    'target_values': [16.5, 20.0, 24.0, 27.5, 31.0, 34.0, 36.5, 39.0, 40.5, 42.5, 44.0, 45.0, 46.5, 47.5, 49.0, 50.0, 51.0, 51.5],
                    'test_date': '2024-01-25',
                    'test_location': '清华大学汽车工程系',
                    'test_engineer': '王工',
                    'remarks': '测试条件：温度23±2℃，湿度50±5%RH'
                }
            ]
            
            # 频率列名
            freq_columns = SoundTransmissionLossModel.get_frequency_columns()
            target_freq_columns = SoundTransmissionLossModel.get_target_frequency_columns()
            
            for data in test_data:
                # 查找对应的零件、材料、厂家
                part = SoundInsulationPartModel.query.filter_by(part_name=data['part_name']).first()
                material = MaterialModel.query.filter_by(
                    material_name=data['material_name'],
                    weight=data['weight']
                ).first()
                manufacturer = MaterialManufacturerModel.query.filter_by(
                    manufacturer_name=data['manufacturer_name']
                ).first()
                
                if not part or not material or not manufacturer:
                    print(f"⚠ 跳过数据：{data['part_name']} - {data['material_name']} - {data['weight']}g/m²（缺少关联数据）")
                    continue
                
                # 创建隔声量记录
                transmission_loss = SoundTransmissionLossModel(
                    part_id=part.id,
                    material_id=material.id,
                    manufacturer_id=manufacturer.id,
                    test_institution=data['test_institution'],
                    test_date=datetime.strptime(data['test_date'], '%Y-%m-%d').date(),
                    test_location=data['test_location'],
                    test_engineer=data['test_engineer'],
                    remarks=data['remarks']
                )
                
                # 设置测试值
                for i, value in enumerate(data['test_values']):
                    if i < len(freq_columns):
                        setattr(transmission_loss, freq_columns[i], value)
                
                # 设置目标值
                for i, value in enumerate(data['target_values']):
                    if i < len(target_freq_columns):
                        setattr(transmission_loss, target_freq_columns[i], value)
                
                db.session.add(transmission_loss)
                print(f"✓ 添加数据：{data['part_name']} - {data['material_name']} - {data['weight']}g/m²")
            
            # 提交事务
            db.session.commit()
            print("✓ 隔声量测试数据插入成功")
            
            # 显示统计信息
            total_records = SoundTransmissionLossModel.query.count()
            print(f"\n📊 数据库统计：")
            print(f"   隔声量记录总数：{total_records}")
            print(f"   零件数量：{len(SoundInsulationPartModel.get_parts_with_transmission_data())}")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ 数据库初始化失败：{str(e)}")
            raise

def main():
    """主函数"""
    print("=" * 50)
    print("垂直入射法隔声量功能数据库初始化")
    print("=" * 50)
    
    try:
        init_database()
        print("\n🎉 数据库初始化完成！")
        print("\n可以通过以下URL访问功能：")
        print("http://localhost:5000/sound_transmission/transmission_loss_query")
        
    except Exception as e:
        print(f"\n❌ 初始化失败：{str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
