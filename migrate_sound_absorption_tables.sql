-- 吸音系数表结构优化迁移脚本
-- 目标：消除冗余字段，使用外键关系

USE nvh_data;

-- 第一步：备份现有数据
CREATE TABLE sound_absorption_coefficients_backup AS 
SELECT * FROM sound_absorption_coefficients;

-- 第二步：重新设计materials表，支持材料规格
-- 先备份现有materials表
CREATE TABLE materials_backup AS 
SELECT * FROM materials;

-- 删除现有materials表
DROP TABLE materials;

-- 创建新的materials表，包含材料规格信息
CREATE TABLE materials (
    id INT PRIMARY KEY AUTO_INCREMENT,
    material_name VARCHAR(100) NOT NULL COMMENT '材料名称',
    thickness DECIMAL(6,2) NOT NULL COMMENT '厚度(mm)',
    weight DECIMAL(8,2) NOT NULL COMMENT '克重(g/m²)',
    description TEXT COMMENT '材料描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_material_spec (material_name, thickness, weight)
) COMMENT '材料规格表';

-- 从备份恢复materials数据
INSERT INTO materials (material_name, thickness, weight, description)
SELECT material_name, thickness, weight, description 
FROM materials_backup;

-- 第三步：创建新的吸声系数表结构
CREATE TABLE sound_absorption_coefficients_new (
    id INT PRIMARY KEY AUTO_INCREMENT,
    part_id INT NOT NULL COMMENT '零件ID',
    material_id INT NOT NULL COMMENT '材料ID',
    manufacturer_id INT COMMENT '厂家ID',
    test_institution VARCHAR(100) COMMENT '测试机构',
    
    -- 测试值 (19个频率点: 125Hz-10000Hz)
    test_value_125 DECIMAL(4,3) COMMENT '125Hz测试值',
    test_value_160 DECIMAL(4,3) COMMENT '160Hz测试值',
    test_value_200 DECIMAL(4,3) COMMENT '200Hz测试值',
    test_value_250 DECIMAL(4,3) COMMENT '250Hz测试值',
    test_value_315 DECIMAL(4,3) COMMENT '315Hz测试值',
    test_value_400 DECIMAL(4,3) COMMENT '400Hz测试值',
    test_value_500 DECIMAL(4,3) COMMENT '500Hz测试值',
    test_value_630 DECIMAL(4,3) COMMENT '630Hz测试值',
    test_value_800 DECIMAL(4,3) COMMENT '800Hz测试值',
    test_value_1000 DECIMAL(4,3) COMMENT '1000Hz测试值',
    test_value_1250 DECIMAL(4,3) COMMENT '1250Hz测试值',
    test_value_1600 DECIMAL(4,3) COMMENT '1600Hz测试值',
    test_value_2000 DECIMAL(4,3) COMMENT '2000Hz测试值',
    test_value_2500 DECIMAL(4,3) COMMENT '2500Hz测试值',
    test_value_3150 DECIMAL(4,3) COMMENT '3150Hz测试值',
    test_value_4000 DECIMAL(4,3) COMMENT '4000Hz测试值',
    test_value_5000 DECIMAL(4,3) COMMENT '5000Hz测试值',
    test_value_6300 DECIMAL(4,3) COMMENT '6300Hz测试值',
    test_value_8000 DECIMAL(4,3) COMMENT '8000Hz测试值',
    test_value_10000 DECIMAL(4,3) COMMENT '10000Hz测试值',
    
    -- 目标值 (19个频率点: 125Hz-10000Hz)
    target_value_125 DECIMAL(4,3) COMMENT '125Hz目标值',
    target_value_160 DECIMAL(4,3) COMMENT '160Hz目标值',
    target_value_200 DECIMAL(4,3) COMMENT '200Hz目标值',
    target_value_250 DECIMAL(4,3) COMMENT '250Hz目标值',
    target_value_315 DECIMAL(4,3) COMMENT '315Hz目标值',
    target_value_400 DECIMAL(4,3) COMMENT '400Hz目标值',
    target_value_500 DECIMAL(4,3) COMMENT '500Hz目标值',
    target_value_630 DECIMAL(4,3) COMMENT '630Hz目标值',
    target_value_800 DECIMAL(4,3) COMMENT '800Hz目标值',
    target_value_1000 DECIMAL(4,3) COMMENT '1000Hz目标值',
    target_value_1250 DECIMAL(4,3) COMMENT '1250Hz目标值',
    target_value_1600 DECIMAL(4,3) COMMENT '1600Hz目标值',
    target_value_2000 DECIMAL(4,3) COMMENT '2000Hz目标值',
    target_value_2500 DECIMAL(4,3) COMMENT '2500Hz目标值',
    target_value_3150 DECIMAL(4,3) COMMENT '3150Hz目标值',
    target_value_4000 DECIMAL(4,3) COMMENT '4000Hz目标值',
    target_value_5000 DECIMAL(4,3) COMMENT '5000Hz目标值',
    target_value_6300 DECIMAL(4,3) COMMENT '6300Hz目标值',
    target_value_8000 DECIMAL(4,3) COMMENT '8000Hz目标值',
    target_value_10000 DECIMAL(4,3) COMMENT '10000Hz目标值',
    
    -- 测试信息
    test_date DATE COMMENT '测试日期',
    test_location VARCHAR(100) COMMENT '测试地点',
    test_engineer VARCHAR(50) COMMENT '测试工程师',
    test_image_path VARCHAR(500) COMMENT '测试图片路径',
    remarks TEXT COMMENT '备注',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 外键约束
    FOREIGN KEY (part_id) REFERENCES sound_insulation_parts(id),
    FOREIGN KEY (material_id) REFERENCES materials(id),
    FOREIGN KEY (manufacturer_id) REFERENCES material_manufacturers(id),
    
    -- 索引
    INDEX idx_part_material (part_id, material_id),
    INDEX idx_test_date (test_date),
    UNIQUE KEY uk_part_material_test (part_id, material_id, test_date)
) COMMENT '吸声系数表（规范化）';

-- 第四步：迁移数据到新表
INSERT INTO sound_absorption_coefficients_new (
    part_id, material_id, manufacturer_id, test_institution,
    test_value_125, test_value_160, test_value_200, test_value_250, test_value_315,
    test_value_400, test_value_500, test_value_630, test_value_800, test_value_1000,
    test_value_1250, test_value_1600, test_value_2000, test_value_2500, test_value_3150,
    test_value_4000, test_value_5000, test_value_6300, test_value_8000, test_value_10000,
    target_value_125, target_value_160, target_value_200, target_value_250, target_value_315,
    target_value_400, target_value_500, target_value_630, target_value_800, target_value_1000,
    target_value_1250, target_value_1600, target_value_2000, target_value_2500, target_value_3150,
    target_value_4000, target_value_5000, target_value_6300, target_value_8000, target_value_10000,
    test_date, test_location, test_engineer, test_image_path, remarks
)
SELECT 
    p.id as part_id,
    m.id as material_id,
    mf.id as manufacturer_id,
    old.test_institution,
    old.test_value_125, old.test_value_160, old.test_value_200, old.test_value_250, old.test_value_315,
    old.test_value_400, old.test_value_500, old.test_value_630, old.test_value_800, old.test_value_1000,
    old.test_value_1250, old.test_value_1600, old.test_value_2000, old.test_value_2500, old.test_value_3150,
    old.test_value_4000, old.test_value_5000, old.test_value_6300, old.test_value_8000, old.test_value_10000,
    old.target_value_125, old.target_value_160, old.target_value_200, old.target_value_250, old.target_value_315,
    old.target_value_400, old.target_value_500, old.target_value_630, old.target_value_800, old.target_value_1000,
    old.target_value_1250, old.target_value_1600, old.target_value_2000, old.target_value_2500, old.target_value_3150,
    old.target_value_4000, old.target_value_5000, old.target_value_6300, old.target_value_8000, old.target_value_10000,
    old.test_date, old.test_location, old.test_engineer, old.test_image_path, old.remarks
FROM sound_absorption_coefficients_backup old
LEFT JOIN sound_insulation_parts p ON p.part_name = old.part_name
LEFT JOIN materials m ON m.material_name = old.material_name AND m.thickness = old.thickness AND m.weight = old.weight
LEFT JOIN material_manufacturers mf ON mf.manufacturer_name = old.material_manufacturer;

-- 第五步：替换旧表
DROP TABLE sound_absorption_coefficients;
RENAME TABLE sound_absorption_coefficients_new TO sound_absorption_coefficients;

-- 验证数据迁移
SELECT 
    COUNT(*) as total_records,
    COUNT(DISTINCT part_id) as unique_parts,
    COUNT(DISTINCT material_id) as unique_materials
FROM sound_absorption_coefficients;

-- 显示迁移结果
SELECT 
    p.part_name,
    m.material_name,
    m.thickness,
    m.weight,
    mf.manufacturer_name,
    COUNT(*) as record_count
FROM sound_absorption_coefficients sac
LEFT JOIN sound_insulation_parts p ON p.id = sac.part_id
LEFT JOIN materials m ON m.id = sac.material_id
LEFT JOIN material_manufacturers mf ON mf.id = sac.manufacturer_id
GROUP BY p.part_name, m.material_name, m.thickness, m.weight, mf.manufacturer_name
ORDER BY p.part_name, m.material_name, m.weight;
